{"/_not-found/page": "/_not-found", "/api/announcements/route": "/api/announcements", "/api/assessments/[id]/route": "/api/assessments/[id]", "/api/attendance/[id]/route": "/api/attendance/[id]", "/api/auth/verify/route": "/api/auth/verify", "/api/assessments/route": "/api/assessments", "/api/attendance/route": "/api/attendance", "/api/cabinets/[id]/route": "/api/cabinets/[id]", "/api/cabinets/route": "/api/cabinets", "/api/classes/[id]/route": "/api/classes/[id]", "/api/classes/route": "/api/classes", "/api/communication/stats/route": "/api/communication/stats", "/api/courses/[id]/route": "/api/courses/[id]", "/api/dashboard/stats/route": "/api/dashboard/stats", "/api/courses/route": "/api/courses", "/api/enrollments/[id]/route": "/api/enrollments/[id]", "/api/enrollments/route": "/api/enrollments", "/api/health/route": "/api/health", "/api/groups/route": "/api/groups", "/api/inter-server/sync/route": "/api/inter-server/sync", "/api/inter-server/auth/validate/route": "/api/inter-server/auth/validate", "/api/inter-server/health/route": "/api/inter-server/health", "/api/leads/[id]/archive/route": "/api/leads/[id]/archive", "/api/leads/[id]/assign-group/route": "/api/leads/[id]/assign-group", "/api/leads/[id]/call/route": "/api/leads/[id]/call", "/api/groups/[id]/route": "/api/groups/[id]", "/api/leads/route": "/api/leads", "/api/messages/route": "/api/messages", "/api/leads/cleanup/route": "/api/leads/cleanup", "/api/leads/[id]/route": "/api/leads/[id]", "/api/notifications/route": "/api/notifications", "/api/students/[id]/certificates/route": "/api/students/[id]/certificates", "/api/students/[id]/payments/route": "/api/students/[id]/payments", "/api/notifications/test/route": "/api/notifications/test", "/api/students/[id]/route": "/api/students/[id]", "/api/students/[id]/assignments/route": "/api/students/[id]/assignments", "/api/students/dropped/route": "/api/students/dropped", "/api/students/route": "/api/students", "/api/teachers/[id]/kpis/route": "/api/teachers/[id]/kpis", "/api/students/current/progress/route": "/api/students/current/progress", "/api/students/current/dashboard/route": "/api/students/current/dashboard", "/api/teachers/[id]/route": "/api/teachers/[id]", "/api/students/[id]/status/route": "/api/students/[id]/status", "/api/teachers/route": "/api/teachers", "/api/users/[id]/route": "/api/users/[id]", "/api/students/[id]/progress/route": "/api/students/[id]/progress", "/api/cabinets/[id]/schedules/route": "/api/cabinets/[id]/schedules", "/api/workflows/route": "/api/workflows", "/api/users/route": "/api/users", "/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/page": "/", "/auth/signin/page": "/auth/signin", "/(dashboard)/dashboard/cabinets/page": "/dashboard/cabinets", "/(dashboard)/dashboard/assessments/page": "/dashboard/assessments", "/(dashboard)/dashboard/attendance/page": "/dashboard/attendance", "/(dashboard)/dashboard/cabinets/[id]/page": "/dashboard/cabinets/[id]", "/(dashboard)/dashboard/communication/page": "/dashboard/communication", "/(dashboard)/dashboard/communication/announcements/page": "/dashboard/communication/announcements", "/(dashboard)/dashboard/communication/messages/page": "/dashboard/communication/messages", "/(dashboard)/dashboard/courses/page": "/dashboard/courses", "/(dashboard)/dashboard/groups/[id]/page": "/dashboard/groups/[id]", "/(dashboard)/dashboard/groups/page": "/dashboard/groups", "/(dashboard)/dashboard/enrollments/page": "/dashboard/enrollments", "/(dashboard)/dashboard/leads/page": "/dashboard/leads", "/(dashboard)/dashboard/settings/page": "/dashboard/settings", "/(dashboard)/dashboard/page": "/dashboard", "/(dashboard)/dashboard/student/assignments/page": "/dashboard/student/assignments", "/(dashboard)/dashboard/student/page": "/dashboard/student", "/(dashboard)/dashboard/student/payments/page": "/dashboard/student/payments", "/(dashboard)/dashboard/student/progress/page": "/dashboard/student/progress", "/(dashboard)/dashboard/students/[id]/page": "/dashboard/students/[id]", "/(dashboard)/dashboard/students/page": "/dashboard/students", "/(dashboard)/dashboard/student/certificates/page": "/dashboard/student/certificates", "/(dashboard)/dashboard/student/schedule/page": "/dashboard/student/schedule", "/(dashboard)/dashboard/test-notifications/page": "/dashboard/test-notifications", "/(dashboard)/dashboard/student/attendance/page": "/dashboard/student/attendance", "/(dashboard)/dashboard/teachers/[id]/page": "/dashboard/teachers/[id]", "/(dashboard)/dashboard/unauthorized/page": "/dashboard/unauthorized", "/(dashboard)/dashboard/teachers/page": "/dashboard/teachers", "/(dashboard)/dashboard/users/page": "/dashboard/users"}