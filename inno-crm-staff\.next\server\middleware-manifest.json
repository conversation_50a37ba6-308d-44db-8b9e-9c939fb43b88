{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico|public).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "V92YlSFXGUdpdxa8tA5bx", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Ch1cDn+BG5IYPLdK2u8ZKmurL9LkvXtyk0kVrS8G9wk=", "__NEXT_PREVIEW_MODE_ID": "45e43c171a3fc593ecdcf02ca3c2e7e2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "60a3fc317d6e2c66bc069c1f73c09ea5c2579e4090fa4c33eac8ef85736a772c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7400728c6b319314aa3c1f2ea306256d0ae370912668c205ad476de44f97c935"}}}, "functions": {}, "sortedMiddleware": ["/"]}